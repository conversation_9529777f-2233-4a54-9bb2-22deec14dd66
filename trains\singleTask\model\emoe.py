import torch
import torch.nn as nn
import torch.nn.functional as F
from ...subNets import BertTextEncoder
from ...subNets.transformers_encoder.transformer import TransformerEncoder
from ...singleTask.model.router import router
from ...singleTask.model.gated_mamba import SIGMALayers
class EMOE(nn.Module):
    """
    EMOE 模型的核心实现。
    它整合了三个模态（文本、音频、视频）的输入，并通过一个动态的、基于专家混合（MoE）的机制来融合它们。
    该模型包含两个关键部分：
    1. Mixture of Modality Experts (MoME): 为每个样本动态计算模态权重。
    2. Unimodal Distillation (UD): 利用单模态的预测能力来指导多模态的学习。
    """
    def __init__(self, args):
        """
        模型初始化。
        参数:
            args (Namespace): 包含所有模型配置和超参数的命名空间。
        """
        super(EMOE, self).__init__()
        # 1. 初始化文本编码器 (可选，使用BERT)
        if args.use_bert:
            self.text_model = BertTextEncoder(use_finetune=args.use_finetune, transformers=args.transformers,
                                              pretrained=args.pretrained)
        self.use_bert = args.use_bert

        # 2. 从args中解包和设置模型参数
        dst_feature_dims, nheads = args.dst_feature_dim_nheads # 目标特征维度和注意力头数
        
        # 根据数据集名称和是否对齐，设置三个模态的序列长度
        if args.dataset_name == 'mosi':
            if args.need_data_aligned:
                self.len_l, self.len_v, self.len_a = 50, 50, 50
            else: # 未对齐的数据有不同的序列长度
                self.len_l, self.len_v, self.len_a = 50, 500, 375
        if args.dataset_name == 'mosei':
            if args.need_data_aligned:
                self.len_l, self.len_v, self.len_a = 50, 50, 50
            else:
                self.len_l, self.len_v, self.len_a = 50, 500, 500
        
        self.aligned = args.need_data_aligned # 是否对齐的标志
        self.orig_d_l, self.orig_d_a, self.orig_d_v = args.feature_dims # 原始特征维度
        self.d_l = self.d_a = self.d_v = dst_feature_dims # 变换后统一的特征维度
        self.num_heads = nheads # Transformer中的多头注意力头数
        self.layers = args.nlevels # Transformer的层数
        self.attn_dropout = args.attn_dropout # 通用注意力dropout
        self.attn_dropout_a = args.attn_dropout_a # 音频专用注意力dropout
        self.attn_dropout_v = args.attn_dropout_v # 视频专用注意力dropout
        self.relu_dropout = args.relu_dropout # ReLU激活后的dropout
        self.embed_dropout = args.embed_dropout # 输入嵌入的dropout
        self.res_dropout = args.res_dropout # 残差连接的dropout
        self.output_dropout = args.output_dropout # 输出层的dropout
        self.text_dropout = args.text_dropout # 文本特征的dropout
        self.attn_mask = args.attn_mask # 是否使用注意力掩码
        self.fusion_method = args.fusion_method # 融合方法 ('sum' 或 'concat')
        output_dim = 1 # 回归任务的输出维度为1
        self.args = args

        # 3. 定义模态投影层 (使用1D卷积将不同维度的输入投影到统一维度)
        self.proj_l = nn.Conv1d(self.orig_d_l, self.d_l, kernel_size=args.conv1d_kernel_size_l, padding=0, bias=False)
        self.proj_a = nn.Conv1d(self.orig_d_a, self.d_a, kernel_size=args.conv1d_kernel_size_a, padding=0, bias=False)
        self.proj_v = nn.Conv1d(self.orig_d_v, self.d_v, kernel_size=args.conv1d_kernel_size_v, padding=0, bias=False)

        # 4. 定义编码器层 (在进入Transformer之前对特征进行初步编码)
        # 注意：这里所有模态共享同一个encoder_c，用于提取模态不变特征 (common features)
        self.encoder_c = nn.Conv1d(self.d_l, self.d_l, kernel_size=1, padding=0, bias=False)
        # 这几个编码器在当前实现中并未使用，可能为未来扩展保留（套用的DMD代码）
        self.encoder_l = nn.Conv1d(self.d_l, self.d_l, kernel_size=1, padding=0, bias=False)
        self.encoder_v = nn.Conv1d(self.d_v, self.d_v, kernel_size=1, padding=0, bias=False)
        self.encoder_a = nn.Conv1d(self.d_a, self.d_a, kernel_size=1, padding=0, bias=False)

        # 5. 为每个模态实例化独立的Transformer编码器
        self.self_attentions_l = self.get_network(self_type='l')
        self.self_attentions_v = self.get_network(self_type='v')
        self.self_attentions_a = self.get_network(self_type='a')
        # 使用新的工厂方法为每个模态创建 SIGMA 序列处理器
        self.sigma_processor_l = self.get_sigma_network(self_type='l')
        self.sigma_processor_v = self.get_sigma_network(self_type='v')
        self.sigma_processor_a = self.get_sigma_network(self_type='a')

        # 6. 为每个单模态定义独立的预测头 (用于Unimodal Distillation)
        self.proj1_l = nn.Linear(self.d_l, self.d_l)
        self.proj2_l = nn.Linear(self.d_l, self.d_l)
        self.out_layer_l = nn.Linear(self.d_l, output_dim)
        self.proj1_v = nn.Linear(self.d_l, self.d_l)
        self.proj2_v = nn.Linear(self.d_l, self.d_l)
        self.out_layer_v = nn.Linear(self.d_l, output_dim)
        self.proj1_a = nn.Linear(self.d_l, self.d_l)
        self.proj2_a = nn.Linear(self.d_l, self.d_l)
        self.out_layer_a = nn.Linear(self.d_l, output_dim)

        # 7. 为融合后的多模态特征定义预测头
        if self.fusion_method == "sum": # 加权求和融合
            self.proj1_c = nn.Linear(self.d_l, self.d_l)
            self.proj2_c = nn.Linear(self.d_l, self.d_l)
            self.out_layer_c = nn.Linear(self.d_l, output_dim)
        elif self.fusion_method == "concat": # 加权拼接融合
            self.proj1_c = nn.Linear(self.d_l*3, self.d_l*3)
            self.proj2_c = nn.Linear(self.d_l*3, self.d_l*3)
            self.out_layer_c = nn.Linear(self.d_l*3, output_dim)

        # 8. 实例化路由网络 (Router Network)，论文MoME部分的核心
        # 输入维度是三个模态特征拼接后的总维度，输出维度是3（对应三个模态的权重）
        self.Router = router(self.orig_d_l * self.len_l + self.orig_d_a * self.len_l + self.orig_d_v * self.len_l, 3, self.args.temperature)
        
        # 9. 定义线性层，用于在数据未对齐时，将音视频的序列长度变换到与文本相同
        self.transfer_a_ali = nn.Linear(self.len_a, self.len_l)
        self.transfer_v_ali = nn.Linear(self.len_v, self.len_l)

        ####################################################################################
        # ICE-MoE特有参数
        self.num_iterations = getattr(args, 'num_iterations', 2)  # 默认2次迭代

        # 迭代权重学习
        self.iteration_weights = nn.Parameter(torch.ones(self.num_iterations) / self.num_iterations)
        print(f"ICE-MoE initialized with {self.num_iterations} iterations")
        
        # 为每次迭代创建独立的路由网络
        self.iterative_routers = nn.ModuleList([
            self._create_router() for _ in range(self.num_iterations)
        ])
        # 每轮迭代的多模态预测头
        self.multi_heads = nn.ModuleList([
            nn.Linear(self.d_l, 1) for _ in range(self.num_iterations)
        ])
        # 迭代反馈模块 (Proj_L, Proj_V, Proj_A)
        self.proj_feedback_l = nn.Linear(self.d_l, self.d_l)
        self.proj_feedback_v = nn.Linear(self.d_l, self.d_v)
        self.proj_feedback_a = nn.Linear(self.d_l, self.d_a)


        ####################################################################################
# ----------------- 步骤 3: 添加新的 SIGMA 模块工厂函数 -----------------
    def get_sigma_network(self, self_type='l', layers=-1):
        """
        工厂方法，用于创建和配置一个 SIGMALayers 模块。
        这个模块将替代原有的 TransformerEncoder 来处理序列数据。
        """
        # 使用在 args 中定义的统一特征维度
        embed_dim = self.d_l
        
        # 从 args 中获取 Mamba 特定的超参数，如果未定义则使用默认值
        # 这使得我们可以在运行脚本时灵活调整这些参数

        d_state = getattr(self.args, 'd_state', 8)
        d_conv = getattr(self.args, 'd_conv', 4)
        expand = getattr(self.args, 'expand', 2)
        #TEST-(emoe) >>  Acc_2: 0.8430  F1_score: 0.8417  Acc_7: 0.4883  MAE: 0.7306  Loss: 0.7311
        # 实例化并返回 SIGMALayers
        return SIGMALayers(
            d_model=embed_dim,
            d_state=d_state,
            d_conv=d_conv,
            expand=expand,
            dropout=self.embed_dropout,  # 复用 DEMO 模型中已有的 dropout 参数
            num_layers=max(self.layers, layers)
        )
    
    def get_network(self, self_type='l', layers=-1):
        """
        一个工厂/辅助方法，用于根据指定的模态类型来创建和配置一个完整的Transformer编码器网络。
        这种设计模式使得为不同模态（文本、音频、视频）创建具有细微差别（如不同的dropout率）
        的专属编码器变得非常方便，同时又最大化地复用了代码。

        参数:
            self_type (str): 指定要创建的编码器所属的模态类型。
                             可选值为 'l' (语言), 'a' (音频), 'v' (视频)。
            layers (int): 可选参数，用于覆盖在args中设置的默认Transformer层数。
                          如果为-1，则使用默认层数。

        返回:
            TransformerEncoder: 一个配置完成的、准备好处理序列数据的Transformer编码器实例。
        """
        # 1. 根据模态类型，选择对应的特征维度和注意力dropout率。
        #    这允许我们为不同模态（例如，音频可能需要比文本更高的dropout率）进行精细化的超参数调整。
        if self_type == 'l':
            embed_dim, attn_dropout = self.d_l, self.attn_dropout
        elif self_type == 'a':
            embed_dim, attn_dropout = self.d_a, self.attn_dropout_a
        elif self_type == 'v':
            embed_dim, attn_dropout = self.d_v, self.attn_dropout_v
        else:
            raise ValueError("未知的网络类型")

        # 2. 实例化并返回一个TransformerEncoder。
        #    这里传入了所有从args中读取的、与Transformer相关的超参数。
        return TransformerEncoder(embed_dim=embed_dim,             # 特征维度，例如 128
                                  num_heads=self.num_heads,         # 多头注意力机制中的"头"数，例如 8
                                  layers=max(self.layers, layers),  # 编码器层数，例如 4
                                  attn_dropout=attn_dropout,        # 注意力权重矩阵的dropout率
                                  relu_dropout=self.relu_dropout,   # 全连接层中ReLU激活后的dropout率
                                  res_dropout=self.res_dropout,     # 残差连接上的dropout率
                                  embed_dropout=self.embed_dropout,# 输入嵌入（embedding）的dropout率
                                  attn_mask=self.attn_mask)         # 是否在自注意力计算中使用掩码

    def get_net(self, name):
        """一个简单的辅助函数，通过名字获取模型的一个层。"""
        return getattr(self, name)

    def _create_router(self):
        """创建路由网络的辅助方法"""
        from .router import router
        return router(
            self.orig_d_l * self.len_l + self.orig_d_a * self.len_l + self.orig_d_v * self.len_l, 
            3, 
            self.args.temperature
        )

    def _prepare_dynamic_router_input(self, current_l, current_v, current_a):
        """基于更新后的特征准备动态路由输入"""
        # 将更新后的特征拼接作为新的路由输入
        # 注意：这里需要将特征维度调整为与原始m_i兼容的格式
        batch_size = current_l.shape[0]
        
        # 简化版本：直接拼接特征
        dynamic_input = torch.cat([current_l, current_v, current_a], dim=-1)
        
        # 如果维度不匹配，需要进行适配
        if dynamic_input.shape[-1] != self.iterative_routers[0].l1.in_features:
            # 使用线性层进行维度适配
            if not hasattr(self, 'dynamic_input_adapter'):
                self.dynamic_input_adapter = nn.Linear(
                    dynamic_input.shape[-1], 
                    self.iterative_routers[0].l1.in_features
                ).to(dynamic_input.device)
            dynamic_input = self.dynamic_input_adapter(dynamic_input)
        
        return dynamic_input

    def iterative_fusion_with_calibration(self, c_l_att, c_v_att, c_a_att, m_i):
        """
        ICE-MoE的核心：具有迭代校准的动态模态专家模型
        
        实现您设计方案中的完整流程：
        步骤 t.1: 单模态专家独立预测
        步骤 t.2: 动态路由决策  
        步骤 t.3: 多模态特征融合
        步骤 t.4: 多模态融合预测
        步骤 t.5: 动态蒸馏校准
        步骤 t.6: 状态更新与信息反馈
        """
        batch_size = c_l_att.shape[0]
        
        # 存储每轮迭代的结果
        iteration_results = {
            'fusions': [],
            'weights': [],
            'unimodal_logits': [],
            'multimodal_logits': [],
            'calibration_losses': []
        }
        
        # 初始化：使用第一步处理的特征
        current_l, current_v, current_a = c_l_att, c_v_att, c_a_att
        
        for iter_idx in range(self.num_iterations):
            # 步骤 t.1: 单模态专家独立预测 (生成"事实依据")
            l_proj = self.proj2_l(
                F.dropout(F.relu(self.proj1_l(current_l), inplace=True), p=self.output_dropout,
                        training=self.training))
            l_proj += current_l # 残差连接
            logits_l = self.out_layer_l(l_proj)
            logits_l_t = logits_l

            v_proj = self.proj2_v(
                F.dropout(F.relu(self.proj1_v(current_v), inplace=True), p=self.output_dropout,
                        training=self.training))
            v_proj += current_v
            logits_v = self.out_layer_v(v_proj)
            logits_v_t = logits_v

            a_proj = self.proj2_a(
                F.dropout(F.relu(self.proj1_a(current_a), inplace=True), p=self.output_dropout,
                        training=self.training))
            a_proj += current_a
            logits_a = self.out_layer_a(a_proj)
            logits_a_t = logits_a

            # 步骤 t.2: 动态路由决策
            if iter_idx == 0:
                router_input = m_i  # 第一轮使用原始数据输入
            else:
                # 第二轮使用更新后的重构特征输入
                router_input = self._prepare_dynamic_router_input(logits_l_t, logits_v_t, logits_a_t)
            
            iter_weights = self.iterative_routers[iter_idx](router_input)

            
            # 步骤 t.3: 多模态特征融合
            #iter_fusion = self._compute_weighted_fusion(current_l, current_v, current_a, iter_weights)
            
            if self.fusion_method == "sum": 
            # 遍历batch中的每个样本，使用其专属的权重 m_w[i] 进行加权求和
                for i in range(iter_weights.shape[0]):
                    c_f = current_l[i] * iter_weights[i][0] + current_v[i] * iter_weights[i][1] + current_a[i] * iter_weights[i][2]
                    if i == 0: 
                        iter_fusion = c_f.unsqueeze(0)
                    else: 
                        iter_fusion = torch.cat([iter_fusion, c_f.unsqueeze(0)], dim=0)   
            elif self.fusion_method == "concat":        
                # 遍历batch中的每个样本，进行加权拼接
                for i in range(iter_weights.shape[0]):
                    c_f = torch.cat([current_l[i] * iter_weights[i][0], current_v[i] * iter_weights[i][1], current_a[i] * iter_weights[i][2]], dim=0) * 3
                    if i == 0: 
                        iter_fusion = c_f.unsqueeze(0)
                    else: 
                        iter_fusion = torch.cat([iter_fusion, c_f.unsqueeze(0)], dim=0)
            
            # 添加残差连接
            residual = torch.mean(torch.stack([current_l, current_v, current_a]), dim=0)
            F_final = iter_fusion + residual
            
            # 步骤 t.4: 多模态融合预测
            logits_multi_t = self.multi_heads[iter_idx](F_final)
            
            # 步骤 t.5: 动态蒸馏校准
            calibration_loss = self._compute_calibration_loss(
                logits_multi_t, logits_l_t, logits_v_t, logits_a_t, iter_weights
            )
            
            # 存储当前轮次结果
            iteration_results['fusions'].append(F_final)
            iteration_results['weights'].append(iter_weights)
            iteration_results['unimodal_logits'].append([logits_l_t, logits_v_t, logits_a_t])
            iteration_results['multimodal_logits'].append(logits_multi_t)
            iteration_results['calibration_losses'].append(calibration_loss)
            
            # 步骤 t.6: 状态更新与信息反馈 (为下一轮做准备)
            if iter_idx < self.num_iterations - 1:
                current_l = current_l + self.proj_feedback_l(F_final)
                current_v = current_v + self.proj_feedback_v(F_final)
                current_a = current_a + self.proj_feedback_a(F_final)
        
        # 加权组合所有迭代的结果
        final_fusion = torch.zeros_like(iteration_results['fusions'][0])
        normalized_weights = F.softmax(self.iteration_weights, dim=0)
        #print("normalized_weights:", normalized_weights)
        
        for i, fusion in enumerate(iteration_results['fusions']):
            final_fusion += normalized_weights[i] * fusion
        
        return final_fusion, iteration_results, iter_weights

    def _compute_calibration_loss(self, logits_multi, logits_l, logits_v, logits_a, iter_weights):
        """计算动态蒸馏校准损失"""
        # 加权组合单模态预测
        batch_size = logits_l.shape[0]
        z_uni_combined = torch.zeros_like(logits_multi)
        
        for i in range(batch_size):
            z_uni_combined[i] = (iter_weights[i][0] * logits_l[i] + 
                            iter_weights[i][1] * logits_v[i] + 
                            iter_weights[i][2] * logits_a[i])
        
        # 计算KL散度
        prob_multi = F.softmax(logits_multi, dim=-1)
        prob_uni = F.softmax(z_uni_combined.detach(), dim=-1)
        
        # 避免log(0)的问题
        prob_multi = torch.clamp(prob_multi, min=1e-8)
        prob_uni = torch.clamp(prob_uni, min=1e-8)
        
        kl_loss = F.kl_div(torch.log(prob_multi), prob_uni, reduction='batchmean')
        return kl_loss
    
    def extract_features(self, text, audio, video):
        """
        一个辅助函数，用于提取模型的中间特征。
        """
        # 1. 文本特征预处理 (如果使用BERT)
        if self.use_bert:
            text = self.text_model(text)
        # 将各模态输入转置为 (batch_size, feature_dim, seq_len) 以适应Conv1d
        x_l = F.dropout(text.transpose(1, 2), p=self.text_dropout, training=self.training)
        x_a = audio.transpose(1, 2)
        x_v = video.transpose(1, 2)
        # 2. 准备送入路由网络(Router)的输入
        # 如果数据未对齐，先通过线性层统一序列长度
        if not self.aligned:
            # permute 用于交换维度以匹配nn.Linear的输入要求
            audio_ = self.transfer_a_ali(audio.permute(0, 2, 1)).permute(0, 2, 1)
            video_ = self.transfer_v_ali(video.permute(0, 2, 1)).permute(0, 2, 1)
            # 拼接三个模态的原始特征
            m_i = torch.cat((text, video_, audio_), dim=2)
        else:
            m_i = torch.cat((text, video, audio), dim=2)
        
        # 3. 调用路由网络，计算模态权重 m_w
        #m_w = self.Router(m_i)

        # 4. 特征投影：使用1D卷积将各模态输入投影到统一的特征维度
        proj_x_l = x_l if self.orig_d_l == self.d_l else self.proj_l(x_l)
        proj_x_a = x_a if self.orig_d_a == self.d_a else self.proj_a(x_a)
        proj_x_v = x_v if self.orig_d_v == self.d_v else self.proj_v(x_v)

        # 5. 共享编码器：所有模态通过一个共享的1D卷积层进行初步编码
        c_l = self.encoder_c(proj_x_l)
        c_v = self.encoder_c(proj_x_v)
        c_a = self.encoder_c(proj_x_a)

        # c_l, c_v, c_a 的维度是 [序列长度, 批次大小, 特征维度]
        # 这是为每个模态准备好的、进入各自专属Transformer前的数据
        c_l = c_l.permute(2, 0, 1)
        c_v = c_v.permute(2, 0, 1)
        c_a = c_a.permute(2, 0, 1)

        # 6. 模态专属Transformer编码与池化
        #    以下三个代码块的逻辑完全相同，分别为每个模态执行：
        #    a. 通过各自的Transformer编码器进行深度特征提取。
        #    b. 对Transformer的输出进行池化，将序列信息压缩成一个固定大小的向量。

        # --- 语言模态处理 (使用SIGMA模块) ---
        # a. 维度转置以匹配SIGMA模块的输入要求: (batch, seq_len, dim)
        c_l_permuted = c_l.permute(1, 0, 2)
        # b. 将序列送入SIGMA处理器
        sigma_output_l = self.sigma_processor_l(c_l_permuted)
        # c. 池化操作：取序列的最后一个时间步的输出作为最终表示
        c_l_att = sigma_output_l[:, -1, :]

        # --- 视频模态处理 (使用SIGMA模块) ---
        c_v_permuted = c_v.permute(1, 0, 2)
        sigma_output_v = self.sigma_processor_v(c_v_permuted)
        c_v_att = sigma_output_v[:, -1, :]
        
        # --- 音频模态处理 (使用SIGMA模块) ---
        c_a_permuted = c_a.permute(1, 0, 2)
        sigma_output_a = self.sigma_processor_a(c_a_permuted)
        c_a_att = sigma_output_a[:, -1, :]

        return c_l_att, c_v_att, c_a_att, m_i

    def forward(self, text, audio, video):
        """
        模型的前向传播逻辑。
        """
        # 1. 特征提取
        c_l_att, c_v_att, c_a_att, m_i = self.extract_features(text, audio, video)
        
        # 2. ICE-MoE核心：迭代校准处理
        final_fusion, iteration_results, iter_weights = self.iterative_fusion_with_calibration(
            c_l_att, c_v_att, c_a_att, m_i
        )
        #print("iter_weights:", iter_weights)


        # 3. 计算单模态的预测输出 (logits)，用于Unimodal Distillation
        l_proj = self.proj2_l(
            F.dropout(F.relu(self.proj1_l(c_l_att), inplace=True), p=self.output_dropout,
                      training=self.training))
        l_proj += c_l_att # 残差连接
        logits_l = self.out_layer_l(l_proj)

        v_proj = self.proj2_v(
            F.dropout(F.relu(self.proj1_v(c_v_att), inplace=True), p=self.output_dropout,
                      training=self.training))
        v_proj += c_v_att
        logits_v = self.out_layer_v(v_proj)

        a_proj = self.proj2_a(
            F.dropout(F.relu(self.proj1_a(c_a_att), inplace=True), p=self.output_dropout,
                      training=self.training))
        a_proj += c_a_att
        logits_a = self.out_layer_a(a_proj)


        # 5. 计算多模态融合后的预测输出
        c_proj = self.proj2_c(
            F.dropout(F.relu(self.proj1_c(final_fusion), inplace=True), p=self.output_dropout,
                      training=self.training))
        c_proj += final_fusion # 残差连接
        logits_c = self.out_layer_c(c_proj)


        # 6. 将所有需要用于计算损失的中间结果打包返回
        res = {
            'logits_c': logits_c,           # 多模态预测结果
            'logits_l': logits_l,           # 文本单模态预测结果
            'logits_v': logits_v,           # 视频单模态预测结果
            'logits_a': logits_a,           # 音频单模态预测结果
            'channel_weight': iter_weights, # 路由网络输出的模态权重
            'c_proj': c_proj,               # 多模态最终特征
            'l_proj': l_proj,               # 文本单模态最终特征
            'v_proj': v_proj,               # 视频单模态最终特征
            'a_proj': a_proj,               # 音频单模态最终特征
            'c_fea': final_fusion,          # 融合后的特征(进入最后proj层之前)
        }
        return res